#!/usr/bin/env node

/**
 * 最终检查脚本 - 确保所有部署修复都正确
 * Final Check Script - Ensure all deployment fixes are correct
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 开始最终检查...')
console.log('🔍 Starting final verification...')

let allChecksPass = true
const issues = []

// 1. 检查FontAwesome图标语法
function checkFontAwesomeIcons() {
  console.log('📝 检查FontAwesome图标语法...')
  
  const indexPath = path.join(process.cwd(), 'pages', 'index.vue')
  const content = fs.readFileSync(indexPath, 'utf8')
  
  // 检查chevron图标是否使用正确语法
  const chevronUpMatch = content.includes("'chevron-up'") && content.includes("['fas',")
  const chevronDownMatch = content.includes("'chevron-down'") && content.includes("['fas',")
  const hasCorrectSyntax = content.includes(":icon=\"['fas', activeFaqIndex === index ? 'chevron-up' : 'chevron-down']\"")

  const isCorrect = (chevronUpMatch && chevronDownMatch) || hasCorrectSyntax
  
  if (!isCorrect) {
    issues.push('❌ FontAwesome chevron图标语法不正确')
    allChecksPass = false
  } else {
    console.log('✅ FontAwesome图标语法正确')
  }
}

// 2. 检查FAQ功能
function checkFAQFunctionality() {
  console.log('📋 检查FAQ功能...')
  
  const indexPath = path.join(process.cwd(), 'pages', 'index.vue')
  const content = fs.readFileSync(indexPath, 'utf8')
  
  // 检查必要的FAQ元素
  const hasActiveFaqIndex = content.includes('activeFaqIndex')
  const hasToggleFaq = content.includes('toggleFaq')
  const hasFaqStructure = content.includes('faq-answer-wrapper') && content.includes('faq-toggle-icon')
  
  if (!hasActiveFaqIndex || !hasToggleFaq || !hasFaqStructure) {
    issues.push('❌ FAQ功能不完整')
    allChecksPass = false
  } else {
    console.log('✅ FAQ功能完整')
  }
}

// 3. 检查高度一致性
function checkHeightConsistency() {
  console.log('📐 检查高度一致性...')
  
  const indexPath = path.join(process.cwd(), 'pages', 'index.vue')
  const content = fs.readFileSync(indexPath, 'utf8')
  
  // 检查preview-panel和editor-panel的高度设置
  const previewPanelMatch = content.match(/\.preview-panel\s*{[^}]*min-height:\s*85vh[^}]*max-height:\s*85vh/s)
  const editorPanelMatch = content.match(/\.editor-panel\s*{[^}]*min-height:\s*85vh[^}]*max-height:\s*85vh/s)
  
  if (!previewPanelMatch || !editorPanelMatch) {
    issues.push('❌ 面板高度一致性设置不正确')
    allChecksPass = false
  } else {
    console.log('✅ 面板高度一致性正确')
  }
}

// 4. 检查Cloudflare配置
function checkCloudflareConfig() {
  console.log('⚙️ 检查Cloudflare配置...')
  
  const configPath = path.join(process.cwd(), 'nuxt.config.cloudflare.js')
  const content = fs.readFileSync(configPath, 'utf8')
  
  // 检查FontAwesome配置
  const hasFontAwesome = content.includes('nuxt-fontawesome') && content.includes('fontawesome:')
  
  // 检查是否没有重复的i18n配置
  const i18nMatches = content.match(/i18n:\s*{/g)
  const hasNoDuplicateI18n = !i18nMatches || i18nMatches.length <= 1
  
  if (!hasFontAwesome) {
    issues.push('❌ Cloudflare配置缺少FontAwesome设置')
    allChecksPass = false
  }
  
  if (!hasNoDuplicateI18n) {
    issues.push('❌ Cloudflare配置有重复的i18n设置')
    allChecksPass = false
  }
  
  if (hasFontAwesome && hasNoDuplicateI18n) {
    console.log('✅ Cloudflare配置正确')
  }
}

// 5. 检查构建脚本
function checkBuildScripts() {
  console.log('🔧 检查构建脚本...')
  
  const packagePath = path.join(process.cwd(), 'package.json')
  const packageContent = fs.readFileSync(packagePath, 'utf8')
  const packageJson = JSON.parse(packageContent)
  
  // 检查必要的脚本
  const hasFixDeployment = packageJson.scripts && packageJson.scripts['fix:deployment']
  const hasVerifyDeployment = packageJson.scripts && packageJson.scripts['verify:deployment']
  const hasBuildCloudflare = packageJson.scripts && packageJson.scripts['build:cloudflare']
  
  if (!hasFixDeployment || !hasVerifyDeployment || !hasBuildCloudflare) {
    issues.push('❌ 构建脚本不完整')
    allChecksPass = false
  } else {
    console.log('✅ 构建脚本完整')
  }
}

// 6. 检查部署脚本文件
function checkDeploymentScripts() {
  console.log('📜 检查部署脚本文件...')
  
  const fixScriptPath = path.join(process.cwd(), 'scripts', 'fix-deployment-issues.js')
  const verifyScriptPath = path.join(process.cwd(), 'scripts', 'verify-deployment.js')
  const buildScriptPath = path.join(process.cwd(), 'scripts', 'cloudflare-build.sh')
  
  if (!fs.existsSync(fixScriptPath)) {
    issues.push('❌ fix-deployment-issues.js 文件不存在')
    allChecksPass = false
  }
  
  if (!fs.existsSync(verifyScriptPath)) {
    issues.push('❌ verify-deployment.js 文件不存在')
    allChecksPass = false
  }
  
  if (!fs.existsSync(buildScriptPath)) {
    issues.push('❌ cloudflare-build.sh 文件不存在')
    allChecksPass = false
  }
  
  if (fs.existsSync(fixScriptPath) && fs.existsSync(verifyScriptPath) && fs.existsSync(buildScriptPath)) {
    console.log('✅ 所有部署脚本文件存在')
  }
}

// 7. 检查CSS样式
function checkCSSStyles() {
  console.log('🎨 检查CSS样式...')
  
  const indexPath = path.join(process.cwd(), 'pages', 'index.vue')
  const content = fs.readFileSync(indexPath, 'utf8')
  
  // 检查FAQ相关样式
  const hasFaqToggleIcon = content.includes('.faq-toggle-icon')
  const hasFaqAnswerWrapper = content.includes('.faq-answer-wrapper')
  const hasFaqExpanded = content.includes('.faq-answer-wrapper.expanded')
  
  if (!hasFaqToggleIcon || !hasFaqAnswerWrapper || !hasFaqExpanded) {
    issues.push('❌ FAQ CSS样式不完整')
    allChecksPass = false
  } else {
    console.log('✅ FAQ CSS样式完整')
  }
}

// 主函数
function main() {
  try {
    checkFontAwesomeIcons()
    checkFAQFunctionality()
    checkHeightConsistency()
    checkCloudflareConfig()
    checkBuildScripts()
    checkDeploymentScripts()
    checkCSSStyles()
    
    console.log('\n' + '='.repeat(50))
    console.log('📊 最终检查报告 / Final Check Report')
    console.log('='.repeat(50))
    
    if (allChecksPass) {
      console.log('🎉 所有检查都通过了！')
      console.log('🎉 All checks passed!')
      console.log('\n✅ 部署修复完成，可以安全部署到生产环境')
      console.log('✅ Deployment fixes complete, safe to deploy to production')
    } else {
      console.log('❌ 发现以下问题：')
      console.log('❌ Found the following issues:')
      issues.forEach(issue => console.log('   ' + issue))
      console.log('\n⚠️  请修复上述问题后再部署')
      console.log('⚠️  Please fix the above issues before deploying')
    }
    
    console.log('\n📋 检查项目：')
    console.log('📋 Checked items:')
    console.log('   - FontAwesome图标语法')
    console.log('   - FAQ折叠功能')
    console.log('   - 面板高度一致性')
    console.log('   - Cloudflare配置')
    console.log('   - 构建脚本')
    console.log('   - 部署脚本文件')
    console.log('   - CSS样式完整性')
    
    process.exit(allChecksPass ? 0 : 1)
  } catch (error) {
    console.error('❌ 检查过程中出现错误:', error.message)
    console.error('❌ Error during check process:', error.message)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}

module.exports = { 
  checkFontAwesomeIcons,
  checkFAQFunctionality,
  checkHeightConsistency,
  checkCloudflareConfig,
  checkBuildScripts,
  checkDeploymentScripts,
  checkCSSStyles
}
