#!/usr/bin/env node

/**
 * Deployment Issues Fix Script
 * Addresses common deployment inconsistencies between development and production
 */

const fs = require('fs')
const path = require('path')

console.log('🔧 Starting deployment issues fix...')

// 1. Fix FontAwesome icon references in generated files
function fixFontAwesomeReferences() {
  console.log('📝 Fixing FontAwesome icon references...')
  
  const distPath = path.join(process.cwd(), 'dist')
  if (!fs.existsSync(distPath)) {
    console.log('⚠️  Dist directory not found, skipping FontAwesome fixes')
    return
  }

  // Find all HTML files in dist
  function findHtmlFiles(dir) {
    const files = []
    const items = fs.readdirSync(dir)
    
    for (const item of items) {
      const fullPath = path.join(dir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory()) {
        files.push(...findHtmlFiles(fullPath))
      } else if (item.endsWith('.html')) {
        files.push(fullPath)
      }
    }
    
    return files
  }

  const htmlFiles = findHtmlFiles(distPath)
  let fixedFiles = 0

  htmlFiles.forEach(filePath => {
    try {
      let content = fs.readFileSync(filePath, 'utf8')
      let modified = false

      // Fix chevron icon references
      if (content.includes('chevron-up') || content.includes('chevron-down')) {
        content = content.replace(/icon="chevron-up"/g, 'icon="[\'fas\', \'chevron-up\']"')
        content = content.replace(/icon="chevron-down"/g, 'icon="[\'fas\', \'chevron-down\']"')
        modified = true
      }

      if (modified) {
        fs.writeFileSync(filePath, content, 'utf8')
        fixedFiles++
      }
    } catch (error) {
      console.warn(`⚠️  Could not process ${filePath}: ${error.message}`)
    }
  })

  console.log(`✅ Fixed FontAwesome references in ${fixedFiles} files`)
}

// 2. Ensure CSS consistency for layout fixes
function ensureCSSConsistency() {
  console.log('🎨 Ensuring CSS consistency...')
  
  const distPath = path.join(process.cwd(), 'dist')
  if (!fs.existsSync(distPath)) {
    console.log('⚠️  Dist directory not found, skipping CSS fixes')
    return
  }

  // Find all CSS files
  function findCssFiles(dir) {
    const files = []
    const items = fs.readdirSync(dir)
    
    for (const item of items) {
      const fullPath = path.join(dir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory()) {
        files.push(...findCssFiles(fullPath))
      } else if (item.endsWith('.css')) {
        files.push(fullPath)
      }
    }
    
    return files
  }

  const cssFiles = findCssFiles(distPath)
  let fixedFiles = 0

  cssFiles.forEach(filePath => {
    try {
      let content = fs.readFileSync(filePath, 'utf8')
      let modified = false

      // Ensure preview and editor panel height consistency
      if (content.includes('.preview-panel') || content.includes('.editor-panel')) {
        // Check if height consistency rules are present
        if (!content.includes('min-height:85vh') || !content.includes('max-height:85vh')) {
          console.log(`📝 Adding height consistency rules to ${path.basename(filePath)}`)
          
          // Add height consistency rules
          const heightRules = `
/* Height consistency fix for preview and editor panels */
.preview-panel, .editor-panel {
  min-height: 85vh !important;
  max-height: 85vh !important;
  overflow-y: auto;
}
`
          content += heightRules
          modified = true
        }
      }

      // Ensure FAQ collapsible styles are present
      if (content.includes('.faq-item') && !content.includes('.faq-toggle-icon')) {
        console.log(`📝 Adding FAQ collapsible styles to ${path.basename(filePath)}`)
        
        const faqStyles = `
/* FAQ collapsible functionality */
.faq-toggle-icon {
  color: var(--accent-primary);
  font-size: 1rem;
  transition: transform 0.3s ease;
  flex-shrink: 0;
}
.faq-item.active .faq-toggle-icon {
  transform: rotate(180deg);
}
.faq-answer-wrapper {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1), 
              padding 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.3s ease;
  opacity: 0;
}
.faq-answer-wrapper.expanded {
  max-height: 600px;
  padding: 0 2rem 1.5rem 2rem;
  opacity: 1;
}
`
        content += faqStyles
        modified = true
      }

      if (modified) {
        fs.writeFileSync(filePath, content, 'utf8')
        fixedFiles++
      }
    } catch (error) {
      console.warn(`⚠️  Could not process ${filePath}: ${error.message}`)
    }
  })

  console.log(`✅ Fixed CSS consistency in ${fixedFiles} files`)
}

// 3. Generate cache-busting headers
function generateCacheHeaders() {
  console.log('🗂️  Generating cache-busting headers...')
  
  const headersPath = path.join(process.cwd(), 'dist', '_headers')
  
  const headers = `# Cache Control Headers for Cloudflare Pages
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()

# Cache static assets for 1 year
/static/*
  Cache-Control: public, max-age=31536000, immutable

# Cache CSS and JS for 1 year with versioning
*.css
  Cache-Control: public, max-age=31536000, immutable
*.js
  Cache-Control: public, max-age=31536000, immutable

# Cache images for 1 month
*.png
  Cache-Control: public, max-age=2592000
*.jpg
  Cache-Control: public, max-age=2592000
*.jpeg
  Cache-Control: public, max-age=2592000
*.gif
  Cache-Control: public, max-age=2592000
*.webp
  Cache-Control: public, max-age=2592000
*.svg
  Cache-Control: public, max-age=2592000

# Cache fonts for 1 year
*.woff
  Cache-Control: public, max-age=31536000, immutable
*.woff2
  Cache-Control: public, max-age=31536000, immutable
*.ttf
  Cache-Control: public, max-age=31536000, immutable
*.eot
  Cache-Control: public, max-age=31536000, immutable

# Don't cache HTML files
*.html
  Cache-Control: public, max-age=0, must-revalidate

# API routes
/api/*
  Cache-Control: no-cache, no-store, must-revalidate
`

  try {
    fs.writeFileSync(headersPath, headers, 'utf8')
    console.log('✅ Generated _headers file for cache control')
  } catch (error) {
    console.warn(`⚠️  Could not write _headers file: ${error.message}`)
  }
}

// 4. Verify critical files exist
function verifyCriticalFiles() {
  console.log('🔍 Verifying critical files...')
  
  const distPath = path.join(process.cwd(), 'dist')
  const criticalFiles = [
    'index.html',
    '_nuxt',
    'favicon.ico',
    'robots.txt',
    'sitemap.xml'
  ]

  let allFilesExist = true

  criticalFiles.forEach(file => {
    const filePath = path.join(distPath, file)
    if (!fs.existsSync(filePath)) {
      console.error(`❌ Critical file missing: ${file}`)
      allFilesExist = false
    } else {
      console.log(`✅ Found: ${file}`)
    }
  })

  return allFilesExist
}

// Main execution
async function main() {
  try {
    fixFontAwesomeReferences()
    ensureCSSConsistency()
    generateCacheHeaders()
    
    const allFilesExist = verifyCriticalFiles()
    
    if (allFilesExist) {
      console.log('🎉 All deployment issues have been addressed!')
      console.log('📋 Summary of fixes applied:')
      console.log('   - Fixed FontAwesome icon references')
      console.log('   - Ensured CSS layout consistency')
      console.log('   - Generated cache-busting headers')
      console.log('   - Verified critical files exist')
    } else {
      console.error('❌ Some critical files are missing. Please check the build process.')
      process.exit(1)
    }
  } catch (error) {
    console.error('❌ Error during deployment fix:', error.message)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}

module.exports = { fixFontAwesomeReferences, ensureCSSConsistency, generateCacheHeaders, verifyCriticalFiles }
