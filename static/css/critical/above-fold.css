/* 关键CSS - 首屏渲染优化 */

/* 基础重置和布局 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  line-height: 1.6;
  background-color: #f8f9fa;
}

/* 导航栏关键样式 */
.main-navbar {
  background-color: #2c3e50 !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  z-index: 1030;
}

.navbar-brand-custom {
  color: #fff !important;
  text-decoration: none;
}

.brand-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.brand-logo {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.brand-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.brand-main {
  color: #fff;
}

.brand-sub {
  font-size: 0.75rem;
  color: #bdc3c7;
  font-weight: 400;
}

/* 主要内容区域 */
.card-editor-section {
  padding-top: 80px;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 主容器样式 - 不覆盖Bootstrap的container-fluid */
.card-editor-section .container-fluid {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .brand-title {
    font-size: 1rem;
  }
  
  .brand-sub {
    font-size: 0.65rem;
  }
  
  .card-editor-section {
    padding-top: 70px;
  }
}

/* 字体加载优化 - 移除重复定义，已在font-face.css中定义 */

/* 预加载关键资源 */
.preload-critical {
  position: absolute;
  left: -9999px;
  opacity: 0;
  pointer-events: none;
}
